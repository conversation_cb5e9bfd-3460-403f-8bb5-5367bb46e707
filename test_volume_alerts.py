#!/usr/bin/env python3

import asyncio
import logging
from services.market.volume_alert_service import get_volume_alert_service
from utils.config import get_watchlist_symbols

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_volume_service():
    print("=== Testing Volume Alert Service ===")
    
    try:
        # Get service instance
        volume_service = get_volume_alert_service()
        
        print(f"Service configuration:")
        print(f"  Monitoring interval: {volume_service.monitoring_interval}s")
        print(f"  MA period: {volume_service.ma_period}")
        print(f"  Thresholds: {volume_service.thresholds}")
        print(f"  Enabled: {volume_service.enabled}")
        
        # Get watchlist symbols
        watchlist_symbols = get_watchlist_symbols()
        print(f"\nWatchlist symbols ({len(watchlist_symbols)}):")
        for symbol in watchlist_symbols:
            print(f"  - {symbol}")
        
        # Test volume check for one symbol
        if watchlist_symbols:
            test_symbol = watchlist_symbols[0]
            print(f"\nTesting volume check for {test_symbol}...")
            
            # Test H4
            h4_alert = await volume_service._check_symbol_volume(test_symbol, '4h')
            if h4_alert:
                print(f"H4 Alert: {h4_alert}")
            else:
                print("No H4 alert")
            
            # Test D1
            d1_alert = await volume_service._check_symbol_volume(test_symbol, '1d')
            if d1_alert:
                print(f"D1 Alert: {d1_alert}")
            else:
                print("No D1 alert")
        
        print("\n✅ Volume service test completed")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_volume_service())
