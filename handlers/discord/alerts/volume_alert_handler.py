#!/usr/bin/env python3
"""
Discord Volume Alert <PERSON>ler - Sends volume spike alerts to Discord
"""

import logging
import discord
from datetime import datetime, timezone
from typing import Dict, List, Any

from utils.config import get_guild_id
from utils.constants import EMOJ<PERSON>, DISCORD_FORMATTING

logger = logging.getLogger(__name__)

class VolumeAlertHandler:
    """Handler for sending volume alerts to Discord"""
    
    def __init__(self, bot):
        self.bot = bot
        self.guild_id = get_guild_id()
        self.channel_name = "🚨-alerts"  # Target channel name
    
    async def handle_volume_alert(self, alert_type: str, alert_data: Dict[str, Any]):
        """Process volume alerts and send to Discord"""
        try:
            if alert_type == "volume_spike":
                embed = await self._create_volume_spike_embed(alert_data)
                await self._send_to_alerts_channel(embed)
            else:
                logger.warning(f"Unknown volume alert type: {alert_type}")
                
        except Exception as e:
            logger.error(f"Error handling volume alert: {e}")
    
    async def _create_volume_spike_embed(self, alert_data: Dict[str, Any]) -> discord.Embed:
        """Create embed for volume spike alerts"""
        alerts = alert_data.get('alerts', [])
        
        if not alerts:
            return None
        
        # Group alerts by threshold for better presentation
        high_alerts = [a for a in alerts if a['threshold'] >= 2.2]
        medium_alerts = [a for a in alerts if 1.8 <= a['threshold'] < 2.2]
        
        # Determine embed color based on highest threshold
        max_threshold = max(alert['threshold'] for alert in alerts)
        if max_threshold >= 2.2:
            color = DISCORD_FORMATTING['colors']['error']  # Red for high volume
            title_emoji = "🔥"
        else:
            color = DISCORD_FORMATTING['colors']['warning']  # Orange for medium volume
            title_emoji = "📈"
        
        embed = discord.Embed(
            title=f"{title_emoji} VOLUME SPIKE ALERT",
            description="**Watchlist symbols showing significant volume increases**",
            color=color,
            timestamp=datetime.now(timezone.utc)
        )
        
        # Add high priority alerts
        if high_alerts:
            high_text = ""
            for alert in high_alerts[:5]:  # Limit to 5 to avoid embed limits
                symbol = alert['symbol'].replace('USDT', '')
                timeframe = alert['timeframe'].upper()
                ratio = alert['volume_ratio']
                price = alert.get('price', 0)
                
                high_text += f"**{symbol}** ({timeframe}) - **{ratio:.1f}x** MA20\n"
                if price:
                    high_text += f"💰 Price: ${price:,.4f}\n"
                high_text += "\n"
            
            embed.add_field(
                name=f"🔥 HIGH VOLUME (≥2.2x MA20)",
                value=high_text or "None",
                inline=False
            )
        
        # Add medium priority alerts
        if medium_alerts:
            medium_text = ""
            for alert in medium_alerts[:5]:  # Limit to 5
                symbol = alert['symbol'].replace('USDT', '')
                timeframe = alert['timeframe'].upper()
                ratio = alert['volume_ratio']
                price = alert.get('price', 0)
                
                medium_text += f"**{symbol}** ({timeframe}) - **{ratio:.1f}x** MA20\n"
                if price:
                    medium_text += f"💰 Price: ${price:,.4f}\n"
                medium_text += "\n"
            
            embed.add_field(
                name=f"📈 MEDIUM VOLUME (1.8-2.2x MA20)",
                value=medium_text or "None",
                inline=False
            )
        
        # Add analysis section
        total_alerts = len(alerts)
        h4_count = len([a for a in alerts if a['timeframe'] == '4h'])
        d1_count = len([a for a in alerts if a['timeframe'] == '1d'])
        
        analysis_text = (
            f"📊 **Total Alerts:** {total_alerts}\n"
            f"⏰ **H4 Timeframe:** {h4_count}\n"
            f"📅 **D1 Timeframe:** {d1_count}\n\n"
            f"💡 **Analysis:**\n"
        )
        
        if max_threshold >= 2.2:
            analysis_text += "🔥 Extremely high volume activity detected\n"
            analysis_text += "⚡ Potential breakout or major news impact\n"
        else:
            analysis_text += "📈 Increased volume activity\n"
            analysis_text += "👀 Monitor for potential price movements\n"
        
        embed.add_field(
            name="📋 Summary",
            value=analysis_text,
            inline=False
        )
        
        # Add footer
        embed.set_footer(
            text="Volume alerts • Based on MA20 • Watchlist symbols only",
            icon_url="https://cdn-icons-png.flaticon.com/512/2830/2830284.png"
        )
        
        return embed
    
    async def _send_to_alerts_channel(self, embed: discord.Embed):
        """Send alert to the 🚨-alerts channel"""
        if not embed:
            return
            
        if self.guild_id:
            try:
                guild = self.bot.get_guild(int(self.guild_id))
                if guild:
                    # Find alerts channel
                    target_channel = None
                    for channel in guild.channels:
                        if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                            if self.channel_name.lower() in channel.name.lower():
                                target_channel = channel
                                break
                    
                    # Create channel if it doesn't exist
                    if not target_channel:
                        try:
                            target_channel = await guild.create_text_channel(
                                name=self.channel_name,
                                topic="Volume spike alerts for watchlist symbols"
                            )
                            logger.info(f"Created alerts channel: #{target_channel.name}")
                        except discord.Forbidden:
                            logger.error("No permission to create alerts channel")
                            return
                        except Exception as e:
                            logger.error(f"Failed to create alerts channel: {e}")
                            return
                    
                    # Send alert
                    if target_channel:
                        await target_channel.send(embed=embed)
                        logger.info(f"Sent volume alert to #{target_channel.name}")
                    else:
                        logger.warning("No suitable channel found for volume alert")
                else:
                    logger.warning(f"Guild not found: {self.guild_id}")
            except Exception as e:
                logger.error(f"Failed to send volume alert to guild: {e}")
        else:
            logger.warning("No guild_id configured for volume alerts")

# Global handler instance
_volume_alert_handler = None

def get_volume_alert_handler(bot) -> VolumeAlertHandler:
    """Get or create volume alert handler instance"""
    global _volume_alert_handler
    if _volume_alert_handler is None:
        _volume_alert_handler = VolumeAlertHandler(bot)
    return _volume_alert_handler
