#!/usr/bin/env python3

# Test different formatting methods
pnl = -0.34236938
tp = 104444.0
sl = 0

print('Testing different format methods:')

# Method 1: .format()
try:
    pnl_text = '${:.2f}'.format(pnl)
    print('Method 1 - pnl_text =', pnl_text)
except Exception as e:
    print('Method 1 error:', e)

# Method 2: % formatting
try:
    pnl_text = '$%.2f' % pnl
    print('Method 2 - pnl_text =', pnl_text)
except Exception as e:
    print('Method 2 error:', e)

# Method 3: round and str
try:
    pnl_text = '$' + str(round(pnl, 2))
    print('Method 3 - pnl_text =', pnl_text)
except Exception as e:
    print('Method 3 error:', e)

# Method 4: f-string with explicit conversion
try:
    pnl_text = f'${pnl:.2f}'
    print('Method 4 - pnl_text =', repr(pnl_text))
except Exception as e:
    print('Method 4 error:', e)
